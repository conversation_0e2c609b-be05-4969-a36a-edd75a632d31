import os
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import tensorflow as tf
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.applications.mobilenet_v2 import preprocess_input
from tensorflow.keras.layers import Dense, Input
from tensorflow.keras.models import Model
from tensorflow.keras.losses import Huber  # Added Huber Loss
import time
from tqdm import tqdm

# === IMPROVED CONFIGURATION ===
TRAIN_IMG_DIR = r'D:\Backup\Recap\Test\catsanddogs.darknet\train'
VALID_IMG_DIR = r'D:\Backup\Recap\Test\catsanddogs.darknet\valid'  # Use for final evaluation
CSV_PATH = r'D:\Backup\Recap\Test\catDog_labels_for_rcnn.csv'
CLASS_NAMES = ['background', 'dog', 'cat']
IMAGE_SIZE = 224
REGION_PROPOSALS = 75  # Increased from 50 for better coverage
IOU_THRESHOLD = 0.3    # Lowered from 0.5 for more positive samples
MAX_BG_RATIO = 4       # Max 4:1 background to object ratio
CONFIDENCE_THRESHOLD = 0.5 # A starting point for NMS
NMS_IOU_THRESHOLD = 0.3    # Non-Maximum Suppression IoU threshold

def load_annotations(csv_path):
    """Load YOLO format annotations and convert to [xmin, ymin, xmax, ymax] format"""
    df = pd.read_csv(csv_path)
    grouped = df.groupby('filename')
    annotations = {}
    
    for filename, group in grouped:
        boxes = []
        labels = []
        
        # Check if the image exists in either train or valid directories
        img_path_train = os.path.join(TRAIN_IMG_DIR, filename)
        img_path_valid = os.path.join(VALID_IMG_DIR, filename)
        
        img = None
        if os.path.exists(img_path_train):
            img = cv2.imread(img_path_train)
        elif os.path.exists(img_path_valid):
            img = cv2.imread(img_path_valid)
            
        if img is None:
            continue
        
        img_h, img_w = img.shape[:2]
        
        for _, row in group.iterrows():
            # Convert YOLO coordinates to absolute coordinates
            x_center, y_center = row['x_center'], row['y_center']
            width, height = row['width'], row['height']
            xmin = int((x_center - width/2) * img_w)
            ymin = int((y_center - height/2) * img_h)
            xmax = int((x_center + width/2) * img_w)
            ymax = int((y_center + height/2) * img_h)
            
            boxes.append([xmin, ymin, xmax, ymax])
            labels.append(row['class'])
            
        if boxes:
            annotations[filename] = list(zip(boxes, labels))
            
    return annotations

print("Loading annotations...")
annotations = load_annotations(CSV_PATH)
print(f"Loaded annotations for {len(annotations)} images")

# Split annotations into train and validation
train_filenames, val_filenames = train_test_split(
    list(annotations.keys()), test_size=0.2, random_state=42
)
train_annotations = {f: annotations[f] for f in train_filenames}
val_annotations = {f: annotations[f] for f in val_filenames}

def compute_iou(boxA, boxB):
    """Compute Intersection over Union between two boxes"""
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    interArea = max(0, xB - xA) * max(0, yB - yA)
    boxAArea = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxBArea = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
    return interArea / float(boxAArea + boxBArea - interArea + 1e-5)

def non_max_suppression(boxes, scores, iou_threshold):
    """
    Applies non-maximum suppression to filter overlapping bounding boxes.
    Assumes boxes are in [xmin, ymin, xmax, ymax] format.
    """
    if len(boxes) == 0:
        return [], []

    boxes = np.array(boxes)
    scores = np.array(scores)
    
    # Check if boxes is not empty and has the correct shape
    if boxes.ndim != 2 or boxes.shape[1] != 4:
        return [], []

    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]
    
    # Get the areas of the bounding boxes
    areas = (x2 - x1 + 1) * (y2 - y1 + 1)
    
    # Sort by confidence score
    order = scores.argsort()[::-1]
    
    keep = []
    while order.size > 0:
        i = order[0]
        keep.append(i)
        
        # Find intersection coordinates
        xx1 = np.maximum(x1[i], x1[order[1:]])
        yy1 = np.maximum(y1[i], y1[order[1:]])
        xx2 = np.minimum(x2[i], x2[order[1:]])
        yy2 = np.minimum(y2[i], y2[order[1:]])
        
        # Find intersection area
        w = np.maximum(0.0, xx2 - xx1 + 1)
        h = np.maximum(0.0, yy2 - yy1 + 1)
        intersection = w * h
        
        # Calculate IoU
        union = areas[i] + areas[order[1:]] - intersection
        iou = intersection / union
        
        # Find indices of boxes to keep
        inds = np.where(iou <= iou_threshold)[0]
        order = order[inds + 1]
    
    final_boxes = [boxes[i] for i in keep]
    final_scores = [scores[i] for i in keep]
    return final_boxes, final_scores

def selective_search(image):
    """Generate region proposals using Selective Search"""
    ss = cv2.ximgproc.segmentation.createSelectiveSearchSegmentation()
    ss.setBaseImage(image)
    ss.switchToSelectiveSearchFast()
    return ss.process()

def build_balanced_training_data(annotations, img_dir, is_validation=False):
    """
    Build training data with balanced classes
    For validation data, checks both validation and training directories
    """
    X, y_cls, y_bbox = [], [], []
    label_encoder = LabelEncoder()
    label_encoder.fit(CLASS_NAMES[1:])  # exclude background

    background_samples = []
    object_samples = []
    
    processed_images = 0
    for filename, gt in tqdm(annotations.items(), desc=f"Building dataset from {img_dir}"):
        # For validation data, check both directories
        if is_validation:
            image_path = os.path.join(img_dir, filename)
            if not os.path.exists(image_path):
                image_path = os.path.join(TRAIN_IMG_DIR, filename)
        else:
            image_path = os.path.join(img_dir, filename)
            
        image = cv2.imread(image_path)
        if image is None: 
            continue
        
        # Use more region proposals for validation data
        if is_validation:
            regions = selective_search(image)[:REGION_PROPOSALS*2]
            match_threshold = IOU_THRESHOLD * 0.8  # Lower threshold for validation
        else:
            regions = selective_search(image)[:REGION_PROPOSALS]
            match_threshold = IOU_THRESHOLD
        
        # Track positive and negative samples for this image
        image_positives = 0
        image_negatives = 0
        
        for (x, y, w, h) in regions:
            x1, y1, x2, y2 = x, y, x + w, y + h
            proposal = [x1, y1, x2, y2]
            roi = image[y1:y2, x1:x2]
            if roi.size == 0 or roi.shape[0] < 20 or roi.shape[1] < 20:
                continue

            # Find best matching ground truth box
            best_iou = 0
            best_gt = None
            for (gt_box, gt_label) in gt:
                iou = compute_iou(proposal, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt = (gt_box, gt_label)

            # Prepare input
            roi_resized = cv2.resize(roi, (IMAGE_SIZE, IMAGE_SIZE))
            roi_input = preprocess_input(roi_resized.astype(np.float32))

            # Assign class and regression targets
            if best_iou >= match_threshold:
                label = best_gt[1]
                gx1, gy1, gx2, gy2 = best_gt[0]
                # Compute regression targets
                pw, ph = x2 - x1, y2 - y1
                dx = (gx1 - x1) / pw
                dy = (gy1 - y1) / ph
                dw = np.log((gx2 - gx1) / pw + 1e-8)
                dh = np.log((gy2 - gy1) / ph + 1e-8)
                y_bbox_val = [dx, dy, dw, dh]
                
                cls_val = label_encoder.transform([label])[0] + 1
                object_samples.append({
                    'x': roi_input,
                    'y_cls': cls_val,
                    'y_bbox': y_bbox_val
                })
                image_positives += 1
            else:
                # Only add background samples if we haven't exceeded the ratio
                if image_negatives < MAX_BG_RATIO * max(1, image_positives):
                    background_samples.append({
                        'x': roi_input,
                        'y_cls': 0,
                        'y_bbox': [0, 0, 0, 0]
                    })
                    image_negatives += 1

    print(f"Collected {len(background_samples)} background samples")
    print(f"Collected {len(object_samples)} object samples")
    if len(object_samples) > 0:
        print(f"Ratio: {len(background_samples)/len(object_samples):.1f}:1")
    
    # Combine and shuffle
    all_samples = background_samples + object_samples
    np.random.shuffle(all_samples)
    
    # Handle empty case
    if len(all_samples) == 0:
        print("Warning: No samples collected!")
        return np.array([]), np.array([]), np.array([]), label_encoder
    
    # Extract arrays
    X = np.array([sample['x'] for sample in all_samples])
    y_cls = np.array([sample['y_cls'] for sample in all_samples])
    y_bbox = np.array([sample['y_bbox'] for sample in all_samples])

    return X, y_cls, y_bbox, label_encoder

def build_balanced_training_data(annotations, img_dir, is_validation=False):
    """
    Build training data with balanced classes
    For validation data, checks both validation and training directories
    """
    X, y_cls, y_bbox = [], [], []
    label_encoder = LabelEncoder()
    label_encoder.fit(CLASS_NAMES[1:])  # exclude background

    background_samples = []
    object_samples = []
    
    processed_images = 0
    for filename, gt in tqdm(annotations.items(), desc=f"Building dataset from {img_dir}"):
        # For validation data, check both directories
        if is_validation:
            image_path = os.path.join(img_dir, filename)
            if not os.path.exists(image_path):
                image_path = os.path.join(TRAIN_IMG_DIR, filename)
        else:
            image_path = os.path.join(img_dir, filename)
            
        image = cv2.imread(image_path)
        if image is None: 
            continue
        
        # Use more region proposals for validation data
        if is_validation:
            regions = selective_search(image)[:REGION_PROPOSALS*2]
            match_threshold = IOU_THRESHOLD * 0.8  # Lower threshold for validation
        else:
            regions = selective_search(image)[:REGION_PROPOSALS]
            match_threshold = IOU_THRESHOLD
        
        # Track positive and negative samples for this image
        image_positives = 0
        image_negatives = 0
        
        for (x, y, w, h) in regions:
            x1, y1, x2, y2 = x, y, x + w, y + h
            proposal = [x1, y1, x2, y2]
            roi = image[y1:y2, x1:x2]
            if roi.size == 0 or roi.shape[0] < 20 or roi.shape[1] < 20:
                continue

            # Find best matching ground truth box
            best_iou = 0
            best_gt = None
            for (gt_box, gt_label) in gt:
                iou = compute_iou(proposal, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt = (gt_box, gt_label)

            # Prepare input
            roi_resized = cv2.resize(roi, (IMAGE_SIZE, IMAGE_SIZE))
            roi_input = preprocess_input(roi_resized.astype(np.float32))

            # Assign class and regression targets
            if best_iou >= match_threshold:
                label = best_gt[1]
                gx1, gy1, gx2, gy2 = best_gt[0]
                # Compute regression targets
                pw, ph = x2 - x1, y2 - y1
                dx = (gx1 - x1) / pw
                dy = (gy1 - y1) / ph
                dw = np.log((gx2 - gx1) / pw + 1e-8)
                dh = np.log((gy2 - gy1) / ph + 1e-8)
                y_bbox_val = [dx, dy, dw, dh]
                
                cls_val = label_encoder.transform([label])[0] + 1
                object_samples.append({
                    'x': roi_input,
                    'y_cls': cls_val,
                    'y_bbox': y_bbox_val
                })
                image_positives += 1
            else:
                # Only add background samples if we haven't exceeded the ratio
                if image_negatives < MAX_BG_RATIO * max(1, image_positives):
                    background_samples.append({
                        'x': roi_input,
                        'y_cls': 0,
                        'y_bbox': [0, 0, 0, 0]
                    })
                    image_negatives += 1

    print(f"Collected {len(background_samples)} background samples")
    print(f"Collected {len(object_samples)} object samples")
    if len(object_samples) > 0:
        print(f"Ratio: {len(background_samples)/len(object_samples):.1f}:1")
    
    # Combine and shuffle
    all_samples = background_samples + object_samples
    np.random.shuffle(all_samples)
    
    # Handle empty case
    if len(all_samples) == 0:
        print("Warning: No samples collected!")
        return np.array([]), np.array([]), np.array([]), label_encoder
    
    # Extract arrays
    X = np.array([sample['x'] for sample in all_samples])
    y_cls = np.array([sample['y_cls'] for sample in all_samples])
    y_bbox = np.array([sample['y_bbox'] for sample in all_samples])

    return X, y_cls, y_bbox, label_encoder

def build_validation_data():
    """Build validation dataset using training images for validation annotations"""
    X, y_cls, y_bbox = [], [], []
    label_encoder = LabelEncoder()
    label_encoder.fit(CLASS_NAMES[1:])  # exclude background

    background_samples = []
    object_samples = []
    
    for filename, gt in tqdm(val_annotations.items(), desc="Building validation dataset"):
        # Try to find the image in either directory
        image_path = os.path.join(VALID_IMG_DIR, filename)
        if not os.path.exists(image_path):
            image_path = os.path.join(TRAIN_IMG_DIR, filename)
            
        image = cv2.imread(image_path)
        if image is None:
            continue
            
        # Use more proposals and lower threshold to ensure we get samples
        regions = selective_search(image)[:REGION_PROPOSALS*2]  
        val_iou_threshold = IOU_THRESHOLD * 0.8  # 20% lower threshold
        
        image_positives = 0
        image_negatives = 0
        
        for (x, y, w, h) in regions:
            x1, y1, x2, y2 = x, y, x + w, y + h
            proposal = [x1, y1, x2, y2]
            roi = image[y1:y2, x1:x2]
            if roi.size == 0 or roi.shape[0] < 20 or roi.shape[1] < 20:
                continue

            # Find best matching ground truth box
            best_iou = 0
            best_gt = None
            for (gt_box, gt_label) in gt:
                iou = compute_iou(proposal, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt = (gt_box, gt_label)

            # Prepare input
            roi_resized = cv2.resize(roi, (IMAGE_SIZE, IMAGE_SIZE))
            roi_input = preprocess_input(roi_resized.astype(np.float32))

            # Assign class and regression targets
            if best_iou >= val_iou_threshold:
                label = best_gt[1]
                gx1, gy1, gx2, gy2 = best_gt[0]
                # Compute regression targets
                pw, ph = x2 - x1, y2 - y1
                dx = (gx1 - x1) / pw
                dy = (gy1 - y1) / ph
                dw = np.log((gx2 - gx1) / pw + 1e-8)
                dh = np.log((gy2 - gy1) / ph + 1e-8)
                
                cls_val = label_encoder.transform([label])[0] + 1
                object_samples.append({
                    'x': roi_input,
                    'y_cls': cls_val,
                    'y_bbox': [dx, dy, dw, dh]
                })
                image_positives += 1
            else:
                # Only add background samples if we haven't exceeded the ratio
                if image_negatives < MAX_BG_RATIO * max(1, image_positives):
                    background_samples.append({
                        'x': roi_input,
                        'y_cls': 0,
                        'y_bbox': [0, 0, 0, 0]
                    })
                    image_negatives += 1

    print(f"Collected {len(background_samples)} background samples")
    print(f"Collected {len(object_samples)} object samples")
    
    # If we still have no samples, create minimal synthetic validation set
    if len(object_samples) == 0 and len(X_train) > 0:
        print("⚠️ No validation samples found! Creating minimal synthetic validation set")
        X = X_train[:min(200, len(X_train))]  # Use a subset of training data
        y_cls = y_cls_train[:min(200, len(X_train))]
        y_bbox = y_bbox_train[:min(200, len(X_train))]
        return X, y_cls, y_bbox, label_encoder
    
    # Combine and shuffle
    all_samples = background_samples + object_samples
    np.random.shuffle(all_samples)
    
    # Extract arrays
    if len(all_samples) > 0:
        X = np.array([sample['x'] for sample in all_samples])
        y_cls = np.array([sample['y_cls'] for sample in all_samples])
        y_bbox = np.array([sample['y_bbox'] for sample in all_samples])
    else:
        # Fallback to empty arrays
        X = np.array([])
        y_cls = np.array([])
        y_bbox = np.array([])

    return X, y_cls, y_bbox, label_encoder

# First, build the training data with the improved function
print("Building balanced training data...")
X_train, y_cls_train, y_bbox_train, label_encoder = build_balanced_training_data(train_annotations, TRAIN_IMG_DIR)
print(f"Training dataset size: {len(X_train)} samples")

# Print class distribution
unique, counts = np.unique(y_cls_train, return_counts=True)
print("Training class distribution:")
for cls, count in zip(unique, counts):
    cls_name = CLASS_NAMES[cls] if cls < len(CLASS_NAMES) else f"Class_{cls}"
    print(f"  {cls_name}: {count} samples ({count/len(y_cls_train)*100:.1f}%)")

# Then try the normal validation approach first
print("\nBuilding validation data...")
X_val, y_cls_val, y_bbox_val, _ = build_balanced_training_data(val_annotations, VALID_IMG_DIR, is_validation=True)
print(f"Validation dataset size: {len(X_val)} samples")

# If validation dataset is empty, use the specialized validation function
if len(X_val) == 0:
    print("\nFixing validation data issue...")
    X_val, y_cls_val, y_bbox_val, _ = build_validation_data()
    print(f"Fixed validation dataset size: {len(X_val)} samples")

# Print class distribution
if len(X_val) > 0:
    unique, counts = np.unique(y_cls_val, return_counts=True)
    print("Validation class distribution:")
    for cls, count in zip(unique, counts):
        cls_name = CLASS_NAMES[cls] if cls < len(CLASS_NAMES) else f"Class_{cls}"
        print(f"  {cls_name}: {count} samples ({count/len(y_cls_val)*100:.1f}%)")

def rcnn_model(num_classes):
    """Create RCNN model with classification and regression heads"""
    base_model = MobileNetV2(weights='imagenet', 
                            include_top=False, 
                            pooling='avg',
                            input_shape=(IMAGE_SIZE, IMAGE_SIZE, 3))
    
    features = base_model.output
    cls_head = Dense(num_classes, activation='softmax', name='class_output')(features)
    bbox_head = Dense(4, name='bbox_output')(features)
    
    model = Model(inputs=base_model.input, outputs=[cls_head, bbox_head])
    
    # Use Huber Loss for more stable bounding box regression
    huber_loss = Huber(delta=1.0)
    
    model.compile(
        optimizer='adam',
        loss={
            'class_output': 'sparse_categorical_crossentropy',
            'bbox_output': huber_loss
        },
        loss_weights={
            'class_output': 1.0,
            'bbox_output': 0.5
        },
        metrics={
            'class_output': 'accuracy'
        }
    )
    return model

print("\nTraining model...")
model = rcnn_model(len(CLASS_NAMES))

# Compute class weights for balanced training
from sklearn.utils.class_weight import compute_class_weight
unique_classes = np.unique(y_cls_train)
class_weights = compute_class_weight('balanced', classes=unique_classes, y=y_cls_train)
class_weight_dict = dict(zip(unique_classes, class_weights))
print(f"Using class weights: {class_weight_dict}")

# Add callbacks for better training
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

callbacks = [
    EarlyStopping(patience=5, restore_best_weights=True, monitor='val_class_output_accuracy'),
    ReduceLROnPlateau(patience=3, factor=0.5, monitor='val_loss')
]

# For multi-output models, we need to structure class weights correctly
# We use sample_weight to apply class weights only to classification loss
sample_weights = np.ones(len(y_cls_train))
for i, cls in enumerate(y_cls_train):
    sample_weights[i] = class_weight_dict[cls]

history = model.fit(
    X_train,
    {'class_output': y_cls_train, 'bbox_output': y_bbox_train},
    validation_data=(X_val, {'class_output': y_cls_val, 'bbox_output': y_bbox_val}),
    epochs=10,
    batch_size=16,
    callbacks=callbacks,
    sample_weight=sample_weights,  # Use sample_weight instead of class_weight
    verbose=1
)

model.save("rcnn_dogcat_model.h5")
print("✅ Model trained and saved")

def evaluate_and_visualize(model, val_annotations, img_dir, label_encoder):
    """
    Performs full evaluation on the validation set, including NMS,
    and visualizes predictions on a sample image.
    """
    val_filenames = list(val_annotations.keys())
    
    # Plot training history
    plt.figure(figsize=(15, 5))
    plt.subplot(1, 3, 1)
    plt.plot(history.history['class_output_accuracy'], label='Train Accuracy')
    plt.plot(history.history['val_class_output_accuracy'], label='Val Accuracy')
    plt.title('Classification Accuracy')
    plt.legend()

    plt.subplot(1, 3, 2)
    plt.plot(history.history['class_output_loss'], label='Train Class Loss')
    plt.plot(history.history['val_class_output_loss'], label='Val Class Loss')
    plt.title('Classification Loss')
    plt.legend()
    
    plt.subplot(1, 3, 3)
    plt.plot(history.history['loss'], label='Train Total Loss')
    plt.plot(history.history['val_loss'], label='Val Total Loss')
    plt.title('Total Loss')
    plt.legend()
    
    plt.tight_layout()
    plt.show()

    print("\n--- Starting Final Model Evaluation on Validation Set ---")
    all_predictions = []
    all_ground_truths = []
    
    for filename in tqdm(val_filenames, desc="Running inference on validation set"):
        image_path = os.path.join(img_dir, filename)
        img = cv2.imread(image_path)
        if img is None: continue
        
        regions = selective_search(img)[:REGION_PROPOSALS]
        rois_to_predict = []
        region_boxes = []
        for (x, y, w, h) in regions:
            x1, y1, x2, y2 = x, y, x+w, y+h
            roi = img[y1:y2, x1:x2]
            if roi.size == 0 or roi.shape[0] < 20 or roi.shape[1] < 20:
                continue
            roi_resized = cv2.resize(roi, (IMAGE_SIZE, IMAGE_SIZE))
            rois_to_predict.append(preprocess_input(roi_resized.astype(np.float32)))
            region_boxes.append([x1, y1, x2, y2])
        
        if not rois_to_predict: continue
        rois_to_predict = np.array(rois_to_predict)
        
        cls_preds, bbox_preds = model.predict(rois_to_predict, verbose=0)
        
        decoded_boxes = []
        scores = []
        class_labels = []
        
        for i, (cls_pred, bbox_pred) in enumerate(zip(cls_preds, bbox_preds)):
            class_idx = np.argmax(cls_pred)
            confidence = cls_pred[class_idx]
            
            if class_idx != 0 and confidence > CONFIDENCE_THRESHOLD: # Ignore background and low confidence
                x1, y1, x2, y2 = region_boxes[i]
                pw, ph = x2 - x1, y2 - y1
                
                # Decode the predicted bounding box
                px1 = x1 + bbox_pred[0] * pw
                py1 = y1 + bbox_pred[1] * ph
                px2 = px1 + pw * np.exp(bbox_pred[2])
                py2 = py1 + ph * np.exp(bbox_pred[3])
                
                decoded_boxes.append([px1, py1, px2, py2])
                scores.append(confidence)
                class_labels.append(CLASS_NAMES[class_idx])

        # Apply NMS
        filtered_boxes, filtered_scores = non_max_suppression(decoded_boxes, scores, NMS_IOU_THRESHOLD)
        
        all_ground_truths.extend(val_annotations.get(filename, []))
        
        for box, score, label in zip(filtered_boxes, filtered_scores, class_labels):
             all_predictions.append({'box': box, 'score': score, 'label': label})

    # Visualize predictions on a random validation image
    sample_filename = np.random.choice(val_filenames)
    sample_img_path = os.path.join(img_dir, sample_filename)
    sample_img = cv2.imread(sample_img_path)
    if sample_img is not None:
        sample_img = cv2.cvtColor(sample_img, cv2.COLOR_BGR2RGB)
        
        # Get predictions for this image
        sample_preds = [p for p in all_predictions if 'box' in p and 'score' in p]
        
        plt.figure(figsize=(10, 10))
        plt.imshow(sample_img)
        ax = plt.gca()
        
        for box, label in val_annotations.get(sample_filename, []):
            x1, y1, x2, y2 = [int(i) for i in box]
            rect = plt.Rectangle((x1, y1), x2 - x1, y2 - y1, fill=False, edgecolor='green', linewidth=2)
            ax.add_patch(rect)
            ax.text(x1, y1 - 5, f'GT: {label}', color='green')

        for i, (box, score) in enumerate(zip(filtered_boxes, filtered_scores)):
            x1, y1, x2, y2 = [int(i) for i in box]
            rect = plt.Rectangle((x1, y1), x2 - x1, y2 - y1, fill=False, edgecolor='red', linewidth=2)
            ax.add_patch(rect)
            ax.text(x1, y1 - 15, f'Pred: {class_labels[i]} ({score:.2f})', color='red')
            
        plt.title(f"Predictions on {sample_filename}")
        plt.axis('off')
        plt.show()

print("\nRunning full evaluation and visualization...")
evaluate_and_visualize(model, val_annotations, VALID_IMG_DIR, label_encoder)

print("Training and evaluation completed!")

def run_inference_on_image(image_path, model, label_encoder, confidence_threshold=0.5, nms_threshold=0.3):
    """
    Run inference on a single image and visualize results
    
    Args:
        image_path: Path to the input image
        model: Trained RCNN model
        label_encoder: Label encoder to decode class predictions
        confidence_threshold: Threshold for detection confidence
        nms_threshold: Threshold for non-maximum suppression
    """
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Could not read image from {image_path}")
        return
    
    # Convert to RGB for display
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Generate region proposals
    print("Generating region proposals...")
    regions = selective_search(img)[:REGION_PROPOSALS]
    
    # Prepare for prediction
    rois = []
    region_boxes = []
    
    for x, y, w, h in regions:
        # Get region coordinates
        x1, y1, x2, y2 = x, y, x+w, y+h
        roi = img[y1:y2, x1:x2]
        
        # Skip small regions
        if roi.size == 0 or roi.shape[0] < 20 or roi.shape[1] < 20:
            continue
            
        # Preprocess the ROI for the model
        roi_resized = cv2.resize(roi, (IMAGE_SIZE, IMAGE_SIZE))
        roi_processed = preprocess_input(roi_resized.astype(np.float32))
        
        rois.append(roi_processed)
        region_boxes.append([x1, y1, x2, y2])
    
    if not rois:
        print("No valid regions found in the image.")
        return
    
    # Convert to numpy array
    rois = np.array(rois)
    
    # Run prediction
    print(f"Running prediction on {len(rois)} regions...")
    cls_preds, bbox_preds = model.predict(rois, verbose=0)
    
    # Process predictions
    boxes = []
    scores = []
    class_ids = []
    
    for i, (cls_pred, bbox_pred) in enumerate(zip(cls_preds, bbox_preds)):
        class_id = np.argmax(cls_pred)
        score = cls_pred[class_id]
        
        # Skip background and low confidence detections
        if class_id == 0 or score < confidence_threshold:
            continue
            
        # Get original region coordinates
        x1, y1, x2, y2 = region_boxes[i]
        w, h = x2 - x1, y2 - y1
        
        # Apply bounding box regression
        pred_x1 = x1 + bbox_pred[0] * w
        pred_y1 = y1 + bbox_pred[1] * h
        pred_x2 = pred_x1 + w * np.exp(bbox_pred[2])
        pred_y2 = pred_y1 + h * np.exp(bbox_pred[3])
        
        # Add to lists
        boxes.append([pred_x1, pred_y1, pred_x2, pred_y2])
        scores.append(score)
        class_ids.append(class_id)
    
    # Apply NMS to remove overlapping boxes
    if boxes:
        final_boxes, final_scores = non_max_suppression(boxes, scores, nms_threshold)
        final_classes = [class_ids[scores.index(score)] for score in final_scores]
    else:
        final_boxes, final_scores, final_classes = [], [], []
    
    # Visualize results
    plt.figure(figsize=(12, 10))
    plt.imshow(img_rgb)
    ax = plt.gca()
    
    # Draw each detection
    for box, score, class_id in zip(final_boxes, final_scores, final_classes):
        x1, y1, x2, y2 = [max(0, int(coord)) for coord in box]
        
        # Draw box
        rect = plt.Rectangle((x1, y1), x2 - x1, y2 - y1, 
                            fill=False, edgecolor='red', linewidth=2)
        ax.add_patch(rect)
        
        # Class name with score
        class_name = CLASS_NAMES[class_id]
        label_text = f"{class_name}: {score:.2f}"
        
        # Add text with background for better visibility
        ax.text(x1, y1-10, label_text, 
                backgroundcolor='white', color='red', fontsize=12)
    
    plt.title(f"Object Detection on {os.path.basename(image_path)}")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    # Print detection summary
    print(f"\nDetected {len(final_boxes)} objects in {os.path.basename(image_path)}:")
    for i, (box, score, class_id) in enumerate(zip(final_boxes, final_scores, final_classes)):
        x1, y1, x2, y2 = [int(coord) for coord in box]
        print(f"{i+1}. {CLASS_NAMES[class_id]} ({score:.2f}) at position [{x1}, {y1}, {x2}, {y2}]")
    
    return {
        'boxes': final_boxes,
        'scores': final_scores,
        'classes': [CLASS_NAMES[class_id] for class_id in final_classes]
    }

# Test on a sample image
test_image_path = "testgun.jpg"  # Replace with your test image
if os.path.exists(test_image_path):
    print(f"\nRunning inference on {test_image_path}...")
    results = run_inference_on_image(test_image_path, model, label_encoder)
else:
    print(f"Test image not found: {test_image_path}")
    # Try with a different test image from the validation set
    if val_filenames:
        sample_filename = val_filenames[0]
        sample_path = os.path.join(VALID_IMG_DIR, sample_filename)
        print(f"Using sample image from validation set: {sample_path}")
        results = run_inference_on_image(sample_path, model, label_encoder)

# Interactive cell for inference on any image
def run_inference_on_user_image():
    # Ask for image path
    # image_path = input("Enter the path to an image file: ")
    image_path = r"D:\Backup\Recap\Test\test3.jpeg"
    # D:\Backup\Recap\Test\catsanddogs.darknet\test\162_png.rf.207bb98454546615d2060ecf13686e8c.jpg
    # Validate path
    if not os.path.exists(image_path):
        print(f"Error: Image not found at path {image_path}")
        return
    
    # Adjust confidence threshold if needed
    confidence = input("Enter confidence threshold (0.0-1.0) or press Enter for default (0.5): ")
    confidence_threshold = 0.5
    if confidence:
        try:
            confidence_threshold = float(confidence)
            if not 0 <= confidence_threshold <= 1:
                print("Confidence threshold must be between 0 and 1. Using default (0.5).")
                confidence_threshold = 0.5
        except ValueError:
            print("Invalid confidence value. Using default (0.5).")
    
    # Run inference
    print(f"Running inference on {image_path} with confidence threshold {confidence_threshold}...")
    results = run_inference_on_image(image_path, model, label_encoder, 
                                    confidence_threshold=confidence_threshold)
    
    return results

# Uncomment the following line to run the interactive cell
run_inference_on_user_image()